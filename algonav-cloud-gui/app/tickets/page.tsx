'use client';

import React, { useState } from 'react';
import {
  Container,
  Typography,
  <PERSON>,
  Button,
  Stack,
  Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { TicketList } from '@/components/ticket/TicketList';
import { TicketDetailDrawer } from '@/components/ticket/TicketDetailDrawer';
import SupportDialog from '@/components/job/SupportDialog';
import {
  useTickets,
  useTicket,
  useCreateTicket,
  useUpdateTicketStatus,
  useAddMessage
} from '@/lib/hooks/useTickets';

interface Ticket {
  id: string;
  title: string;
  description?: string;
  status: 'open' | 'in_progress' | 'waiting_on_customer' | 'resolved' | 'closed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  creator: { id: string; email: string };
  assignee?: { id: string; email: string };
  ticket_targets?: Array<{ target_type: string; target_id: number }>;
}

export default function TicketsPage() {
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [supportDialogOpen, setSupportDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [supportReason, setSupportReason] = useState('');
  const [supportAdditionalInfo, setSupportAdditionalInfo] = useState('');

  // Hooks
  const { data: ticketsData, isLoading, error } = useTickets({
    status: statusFilter,
    myTickets: true
  });
  const { data: selectedTicket, isLoading: ticketLoading } = useTicket(selectedTicketId);
  const createTicket = useCreateTicket();
  const updateTicketStatus = useUpdateTicketStatus();
  const addMessage = useAddMessage();

  const tickets = ticketsData || [];

  const handleTicketClick = (ticket: Ticket) => {
    setSelectedTicketId(ticket.id);
    setDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedTicketId(null);
  };

  const handleSupportReasonChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSupportReason(event.target.value);
  };

  const handleSupportAdditionalInfoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSupportAdditionalInfo(event.target.value);
  };

  const handleCloseSupportDialog = () => {
    setSupportDialogOpen(false);
    setSupportReason('');
    setSupportAdditionalInfo('');
  };

  const handleStatusChange = async (ticketId: string, status: string) => {
    await updateTicketStatus.mutateAsync({ id: ticketId, status });
  };

  const handleAddMessage = async (ticketId: string, message: string) => {
    await addMessage.mutateAsync({ ticketId, body: message });
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
  };

  const handleSearch = (search: string) => {
    setSearchTerm(search);
  };

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error">
          Failed to load tickets: {error.message}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Support Tickets
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your support requests and track their progress
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setSupportDialogOpen(true)}
        >
          Create Ticket
        </Button>
      </Stack>

      <TicketList
        tickets={tickets}
        loading={isLoading}
        onTicketClick={handleTicketClick}
        onStatusFilter={handleStatusFilter}
        onSearch={handleSearch}
      />

      <TicketDetailDrawer
        open={drawerOpen}
        ticket={selectedTicket}
        onClose={handleCloseDrawer}
        onStatusChange={handleStatusChange}
        onAddMessage={handleAddMessage}
        loading={ticketLoading}
      />

      <SupportDialog
        open={supportDialogOpen}
        onClose={handleCloseSupportDialog}
        taskId={null}
        reason={supportReason}
        additionalInfo={supportAdditionalInfo}
        supportType="general"
        onReasonChange={handleSupportReasonChange}
        onAdditionalInfoChange={handleSupportAdditionalInfoChange}
      />
    </Container>
  );
}
