import React, { memo, forwardRef } from 'react';
import {
  Too<PERSON>ip,
  IconButton,
  Stack,
  Chip,
  Checkbox,
  CircularProgress,
  Box,
  Typography
} from '@mui/material';
import SupportIcon from '../common/SupportIcon';
import DownloadIcon from '../common/DownloadIcon';
import { StyledTableRow, StyledTableCell } from '../common/TablePresets';
import { Task } from '../../types/job';
import { formatDate, getStatusDisplay, getFileTypeLabel } from '../../utils/jobUtils';

interface TaskRowProps {
  task: Task;
  isSelected: boolean;
  onToggleSelect: (taskId: number) => void;
  onDownloadFile: (taskId: number, result: any) => void;
  onDownloadTaskFiles: (taskId: number) => void;
  onOpenSupportDialog: (taskId: number, datasetName: string, jobId: string) => void; // Changed batchId to jobId
  downloadingFiles: Record<string, boolean>;
  jobId: string; // Changed batchId to jobId
}

const TaskRow = forwardRef<HTMLTableRowElement, TaskRowProps>(({
  task,
  isSelected,
  onToggleSelect,
  onDownloadFile,
  onDownloadTaskFiles,
  onOpenSupportDialog,
  downloadingFiles,
  jobId // Changed batchId to jobId
}, ref) => {
  const fileTypes = ['kml', 'csv', 'pdf'];
  
  return (
    <StyledTableRow key={task.id} ref={ref}>
      <StyledTableCell padding="checkbox">
        <Checkbox
          checked={isSelected}
          onChange={() => onToggleSelect(task.id)}
          inputProps={{ 'aria-labelledby': `task-${task.id}` }}
          size="small"
        />
      </StyledTableCell>

      <StyledTableCell sx={{ maxWidth: '300px', width: '300px' }}>
        <Tooltip
          title={task.dataset?.description || ''}
          arrow
          placement="top"
        >
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            overflow: 'hidden',
            lineHeight: 1.5,
            minHeight: task.dataset?.description ? 'auto' : '20px',
            maxWidth: '100%'
          }}>
            <Typography
              variant="body2"
              component="span"
              sx={{
                fontWeight: 'medium',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                flexShrink: 0,
                maxWidth: task.dataset?.description ? '120px' : '100%'
              }}
            >
              {task.dataset?.name || 'N/A'}
            </Typography>
            {task.dataset?.description && (
              <>
                <Typography
                  component="span"
                  sx={{ mx: 1, color: 'text.secondary', flexShrink: 0 }}
                >
                  —
                </Typography>
                <Typography
                  variant="body2"
                  component="span"
                  sx={{
                    color: 'text.secondary',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    flex: 1,
                    minWidth: 0
                  }}
                >
                  {task.dataset.description}
                </Typography>
              </>
            )}
          </Box>
        </Tooltip>
      </StyledTableCell>
      <StyledTableCell>
        <Stack direction="row" spacing={1} alignItems="center">
          <Tooltip title={getStatusDisplay(task.status, true).tooltip || ''} arrow>
            <Chip
              label={getStatusDisplay(task.status, true).text}
              color={getStatusDisplay(task.status, true).color as 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | undefined}
              size="small"
            />
          </Tooltip>
        </Stack>
      </StyledTableCell>
      <StyledTableCell>
        <Tooltip title="Request support for this task">
          <IconButton
            size="small"
            onClick={() => onOpenSupportDialog(task.id, task.dataset?.name || '', jobId)} // Changed batchId to jobId
            aria-label={`Request support for task ${task.id}`}
            sx={{
              color: 'text.secondary',
              '&:hover': {
                color: 'primary.main'
              },
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <SupportIcon width={24} height={24} />
          </IconButton>
        </Tooltip>
      </StyledTableCell>
      <StyledTableCell>{formatDate(task.created_at)}</StyledTableCell>

      {fileTypes.map((type) => {
        const result = task.task_results?.find(r => 
          type === 'csv' ? 
            r.file_type?.toLowerCase() === 'csv_pva' :
            r.file_type?.toLowerCase() === type
        );
        
        const fileId = result ? `${task.id}-${result.id}` : '';
        const isDownloading = fileId && downloadingFiles[fileId];
        
        return (
          <StyledTableCell key={type} align="center">
            {result && (
              <Tooltip title={`Download ${getFileTypeLabel(type)}`}>
                <span>
                  <IconButton
                    size="medium"
                    onClick={() => onDownloadFile(task.id, result)}
                    disabled={!!isDownloading}
                    aria-label={`Download ${getFileTypeLabel(type)} for task ${task.id}`}
                    sx={{ 
                      color: 'text.secondary',
                      '&:hover': {
                        color: 'primary.main'
                      }
                    }}
                  >
                    {isDownloading ? (
                      <CircularProgress size={20} />
                    ) : (
                      <DownloadIcon fileType={type} isMulti={false} />
                    )}
                  </IconButton>
                </span>
              </Tooltip>
            )}
          </StyledTableCell>
        );
      })}
      
      <StyledTableCell align="center">
        {task.task_results && task.task_results.length > 0 && (
          <Tooltip title="Download all files for this task">
            <span>
              <IconButton
                size="medium"
                onClick={() => onDownloadTaskFiles(task.id)}
                disabled={!!downloadingFiles[`task-${task.id}`]}
                aria-label={`Download all files for task ${task.id}`}
                sx={{ 
                  color: 'text.secondary',
                  '&:hover': {
                    color: 'primary.main'
                  }
                }}
              >
                {downloadingFiles[`task-${task.id}`] ? (
                  <CircularProgress size={20} />
                ) : (
                  <DownloadIcon fileType="all" isMulti={false} />
                )}
              </IconButton>
            </span>
          </Tooltip>
        )}
      </StyledTableCell>
    </StyledTableRow>
  );
});

TaskRow.displayName = 'TaskRow';

export default memo(TaskRow);
